import React, { createContext, useContext, useState, useEffect } from 'react';
import { User, TokenResponse } from '@otrs-ai-powered/shared';
import { authService } from '../services/authService';

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (username: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  refreshToken: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  isAuthenticated: false,
  isLoading: true,
  login: async () => {},
  logout: async () => {},
  refreshToken: async () => {},
});

export const useAuth = () => useContext(AuthContext);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // For demo purposes, automatically log in a demo user
    const initDemoAuth = async () => {
      try {
        // Check if we already have a demo user
        const existingToken = localStorage.getItem('accessToken');
        if (existingToken) {
          try {
            const userData = await authService.getCurrentUser();
            setUser(userData);
            setIsLoading(false);
            return;
          } catch (error) {
            // Token might be invalid, continue with demo login
            localStorage.removeItem('accessToken');
            localStorage.removeItem('refreshToken');
          }
        }

        // Create demo user for development
        const demoUser: User = {
          id: 'demo-user-1',
          username: 'demo',
          email: '<EMAIL>',
          roles: ['user'],
          permissions: ['chat:send', 'tickets:create', 'tickets:view'],
          metadata: {
            firstName: 'Demo',
            lastName: 'User',
            isActive: true,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          },
        };

        // Set demo tokens
        localStorage.setItem('accessToken', 'demo-access-token');
        localStorage.setItem('refreshToken', 'demo-refresh-token');

        setUser(demoUser);
      } catch (error) {
        console.error('Failed to initialize demo auth:', error);
      } finally {
        setIsLoading(false);
      }
    };

    initDemoAuth();
  }, []);

  const login = async (username: string, password: string) => {
    setIsLoading(true);
    try {
      const tokenResponse: TokenResponse = await authService.login(username, password);
      localStorage.setItem('accessToken', tokenResponse.accessToken);
      localStorage.setItem('refreshToken', tokenResponse.refreshToken);

      const userData = await authService.getCurrentUser();
      setUser(userData);
    } catch (error) {
      console.error('Login failed:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    setIsLoading(true);
    try {
      await authService.logout();
      localStorage.removeItem('accessToken');
      localStorage.removeItem('refreshToken');
      setUser(null);
    } catch (error) {
      console.error('Logout failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const refreshToken = async () => {
    const refreshTokenValue = localStorage.getItem('refreshToken');
    if (!refreshTokenValue) {
      setUser(null);
      return;
    }

    try {
      const tokenResponse: TokenResponse = await authService.refreshToken(refreshTokenValue);
      localStorage.setItem('accessToken', tokenResponse.accessToken);
      localStorage.setItem('refreshToken', tokenResponse.refreshToken);

      const userData = await authService.getCurrentUser();
      setUser(userData);
    } catch (error) {
      console.error('Token refresh failed:', error);
      localStorage.removeItem('accessToken');
      localStorage.removeItem('refreshToken');
      setUser(null);
    }
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        isAuthenticated: !!user,
        isLoading,
        login,
        logout,
        refreshToken,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};
