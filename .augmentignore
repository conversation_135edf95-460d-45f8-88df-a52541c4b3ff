# Dependencies and build artifacts
node_modules/
dist/
build/
.next/
out/
.turbo/

# Environment and configuration files
.env*
*.log
.npm
.yarn-integrity

# Cache directories
.cache/
.parcel-cache/
.rpt2_cache/
.rts2_cache_*/
.nyc_output/
coverage/
*.tsbuildinfo

# Editor and OS files
.vscode/
.idea/
*.swp
*.swo
.DS_Store
Thumbs.db

# Temporary files
tmp/
temp/
*.tmp
*.temp

# Python cache (for model-tuning)
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
venv/
.venv/
env/
ENV/

# Jupyter notebooks checkpoints
.ipynb_checkpoints/

# Model and data files
*.model
*.pkl
*.joblib
models/
checkpoints/
data/
datasets/
*.csv
*.parquet

# Vector database files
*.db
*.sqlite
*.sqlite3

# Documentation build outputs
docs/_build/
docs/build/

# Test coverage
*.lcov

# Package manager files
yarn-error.log
npm-debug.log*
.pnpm-debug.log*

# Docker files
.dockerignore

# SSL certificates
*.pem
*.key
*.crt

# Backup files
*.bak
*.backup

# Large binary files
*.zip
*.tar.gz
*.rar
*.7z

# Media files
*.mp4
*.avi
*.mov
*.wmv
*.flv
*.webm
*.mp3
*.wav
*.flac
*.aac
*.ogg
*.wma
*.jpg
*.jpeg
*.png
*.gif
*.bmp
*.tiff
*.svg
*.ico
*.webp

# Exclude specific large directories that don't need indexing
packages/*/node_modules/
packages/*/dist/
packages/*/build/
packages/*/coverage/
