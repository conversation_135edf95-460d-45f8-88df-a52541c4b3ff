import { v4 as uuidv4 } from 'uuid';
import { Document, DocumentWithEmbedding } from '../types';
import { VectorDbService } from '../storage/vector-db.service';
import { EmbeddingService } from '../embeddings/embedding.service';
import { logger } from '../utils/logger';

/**
 * Service for indexing documents
 */
export class IndexerService {
  constructor(
    private vectorDbService: VectorDbService,
    private embeddingService: EmbeddingService
  ) {
    logger.info('Initializing indexer service');
  }

  /**
   * Index documents in the vector database
   * @param documents Documents to index
   * @returns Result of the indexing operation
   */
  async indexDocuments(documents: Document[]): Promise<{ count: number }> {
    try {
      logger.info(`Indexing ${documents.length} documents`);
      
      // Ensure all documents have IDs
      const docsWithIds = documents.map(doc => ({
        ...doc,
        id: doc.id || uuidv4(),
      }));
      
      // Generate embeddings for all documents
      const embeddings = await this.embeddingService.generateEmbeddings(
        docsWithIds.map(doc => doc.content)
      );
      
      // Combine documents with their embeddings
      const docsWithEmbeddings: DocumentWithEmbedding[] = docsWithIds.map((doc, i) => ({
        ...doc,
        embedding: embeddings[i],
      }));
      
      // Store documents in the vector database
      const result = await this.vectorDbService.storeDocuments(docsWithEmbeddings);
      
      logger.info(`Successfully indexed ${result.count} documents`);
      
      return result;
    } catch (error) {
      logger.error('Error indexing documents:', error);
      throw new Error(`Failed to index documents: ${error.message}`);
    }
  }

  /**
   * Process and chunk a document for indexing
   * @param content Document content
   * @param metadata Document metadata
   * @param chunkSize Size of each chunk
   * @param chunkOverlap Overlap between chunks
   * @returns Array of chunked documents
   */
  chunkDocument(
    content: string,
    metadata: Record<string, any>,
    chunkSize: number = 1000,
    chunkOverlap: number = 200
  ): Document[] {
    try {
      logger.info(`Chunking document with size=${chunkSize}, overlap=${chunkOverlap}`);
      
      // Simple chunking by character count
      // In a real implementation, you might want to chunk by sentences or paragraphs
      
      const chunks: Document[] = [];
      
      if (content.length <= chunkSize) {
        // Document is small enough to be a single chunk
        chunks.push({
          id: uuidv4(),
          content,
          metadata,
        });
      } else {
        // Split document into overlapping chunks
        let startIndex = 0;
        
        while (startIndex < content.length) {
          const endIndex = Math.min(startIndex + chunkSize, content.length);
          const chunkContent = content.substring(startIndex, endIndex);
          
          chunks.push({
            id: uuidv4(),
            content: chunkContent,
            metadata: {
              ...metadata,
              chunk: chunks.length + 1,
              startIndex,
              endIndex,
            },
          });
          
          // Move to next chunk with overlap
          startIndex = endIndex - chunkOverlap;
          
          // Ensure we make progress
          if (startIndex <= 0) {
            startIndex = endIndex;
          }
        }
      }
      
      logger.info(`Created ${chunks.length} chunks from document`);
      
      return chunks;
    } catch (error) {
      logger.error('Error chunking document:', error);
      throw new Error(`Failed to chunk document: ${error.message}`);
    }
  }

  /**
   * Delete documents from the index
   * @param documentIds IDs of documents to delete
   * @returns Result of the deletion operation
   */
  async deleteDocuments(documentIds: string[]): Promise<{ count: number }> {
    try {
      logger.info(`Deleting ${documentIds.length} documents from index`);
      
      const result = await this.vectorDbService.deleteDocuments(documentIds);
      
      logger.info(`Successfully deleted ${result.count} documents`);
      
      return result;
    } catch (error) {
      logger.error('Error deleting documents:', error);
      throw new Error(`Failed to delete documents: ${error.message}`);
    }
  }
}
