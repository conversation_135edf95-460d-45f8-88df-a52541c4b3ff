import { <PERSON>du<PERSON> } from '@nestjs/common';
import { <PERSON>t<PERSON><PERSON>roller } from './chat.controller';
import { ChatService } from './chat.service';
import { ChatGateway } from './chat.gateway';
import { LlmModule } from '../llm/llm.module';
import { McpClientModule } from '../mcp-client/mcp-client.module';
import { AuthModule } from '../auth/auth.module';

@Module({
  imports: [LlmModule, McpClientModule, AuthModule],
  controllers: [ChatController],
  providers: [ChatService, ChatGateway],
  exports: [ChatService],
})
export class ChatModule {}
