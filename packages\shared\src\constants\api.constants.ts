/**
 * API endpoints for the chat service
 */
export const CHAT_API = {
  SEND_MESSAGE: '/chat/messages',
  GET_HISTORY: '/chat/history',
  GET_SESSION: '/chat/sessions/:sessionId',
  CREATE_SESSION: '/chat/sessions',
  DELETE_SESSION: '/chat/sessions/:sessionId',
};

/**
 * API endpoints for authentication
 */
export const AUTH_API = {
  LOGIN: '/auth/login',
  LOGOUT: '/auth/logout',
  REFRESH_TOKEN: '/auth/refresh-token',
  REGISTER: '/auth/register',
  ME: '/auth/me',
};

/**
 * API endpoints for OTRS integration
 */
export const OTRS_API = {
  CREATE_TICKET: '/otrs/tickets',
  UPDATE_TICKET: '/otrs/tickets/:ticketId',
  GET_TICKET: '/otrs/tickets/:ticketId',
  SEARCH_TICKETS: '/otrs/tickets/search',
  GET_QUEUES: '/otrs/queues',
  GET_AGENTS: '/otrs/agents',
};

/**
 * WebSocket events
 */
export const WEBSOCKET_EVENTS = {
  CONNECT: 'connect',
  DISCONNECT: 'disconnect',
  MESSAGE: 'message',
  TYPING: 'typing',
  ERROR: 'error',
};
