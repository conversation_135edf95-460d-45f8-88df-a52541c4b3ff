import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ChatMessage, Tool, ToolCall } from '@otrs-ai-powered/shared';
import {
  ILlmProvider,
  LlmProviderConfig,
  LlmProviderType,
  LlmCompletionResponse
} from './interfaces/llm-provider.interface';
import { LlmProviderFactory } from './providers/llm-provider.factory';

@Injectable()
export class LlmService implements OnModuleInit {
  private readonly logger = new Logger(LlmService.name);
  private provider: ILlmProvider;

  constructor(
    private readonly configService: ConfigService,
    private readonly providerFactory: LlmProviderFactory,
  ) {}

  async onModuleInit() {
    await this.initializeProvider();
  }

  private async initializeProvider(): Promise<void> {
    try {
      // Get provider configuration from environment
      const providerType = this.configService.get<string>('llm.provider', 'google') as LlmProviderType;
      const config: LlmProviderConfig = {
        apiKey: this.configService.get<string>('llm.apiKey', ''),
        model: this.configService.get<string>('llm.model', 'gemini-2.0-flash'),
        temperature: this.configService.get<number>('llm.temperature', 0.7),
        maxTokens: this.configService.get<number>('llm.maxTokens', 2000),
        timeout: this.configService.get<number>('llm.timeout', 30000),
        baseUrl: this.configService.get<string>('llm.baseUrl'),
      };

      this.logger.log(`Initializing LLM provider: ${providerType} with model: ${config.model}`);

      // Create the provider
      this.provider = this.providerFactory.createProvider(providerType, config);

      // Validate the configuration
      const isValid = await this.provider.validateConfig();
      if (!isValid) {
        this.logger.warn(`Provider ${providerType} configuration validation failed`);
      } else {
        this.logger.log(`Provider ${providerType} initialized successfully`);
      }
    } catch (error) {
      this.logger.error('Failed to initialize LLM provider:', error);
      throw error;
    }
  }

  async generateResponse(
    messages: ChatMessage[],
    tools?: Tool[],
  ): Promise<{ content: string; toolCalls?: ToolCall[] }> {
    try {
      if (!this.provider) {
        throw new Error('LLM provider not initialized');
      }

      this.logger.debug(`Generating response using ${this.provider.getProviderName()}`);
      const response: LlmCompletionResponse = await this.provider.generateCompletion(messages, tools);

      return {
        content: response.content,
        toolCalls: response.toolCalls,
      };
    } catch (error) {
      this.logger.error('Error generating LLM response:', error);
      return {
        content: 'I apologize, but I encountered an error processing your request. Please try again later.',
      };
    }
  }

  /**
   * Get information about the current provider
   */
  getProviderInfo(): { name: string; model: string } | null {
    if (!this.provider) {
      return null;
    }

    return {
      name: this.provider.getProviderName(),
      model: this.provider.getModel(),
    };
  }

  /**
   * Switch to a different provider (useful for testing or runtime switching)
   */
  async switchProvider(providerType: LlmProviderType, config: LlmProviderConfig): Promise<void> {
    try {
      this.logger.log(`Switching to provider: ${providerType}`);

      const newProvider = this.providerFactory.createProvider(providerType, config);
      const isValid = await newProvider.validateConfig();

      if (!isValid) {
        throw new Error(`Provider ${providerType} configuration validation failed`);
      }

      this.provider = newProvider;
      this.logger.log(`Successfully switched to provider: ${providerType}`);
    } catch (error) {
      this.logger.error('Failed to switch provider:', error);
      throw error;
    }
  }
}
