import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  OnGatewayConnection,
  OnGatewayDisconnect,
  WsException,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { JwtService } from '@nestjs/jwt';
import { WEBSOCKET_EVENTS } from '@otrs-ai-powered/shared';
import { AuthService } from '../auth/auth.service';

@WebSocketGateway({
  cors: {
    origin: '*',
  },
})
export class ChatGateway implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  // Map to store user connections
  private userSockets = new Map<string, Set<string>>();

  constructor(
    private jwtService: JwtService,
    private authService: AuthService,
  ) {}

  async handleConnection(client: Socket) {
    try {
      // Authenticate the client
      const token = client.handshake.auth.token;
      if (!token) {
        throw new WsException('Unauthorized');
      }

      let payload: any;
      let user: any;

      // Check if this is a demo token
      if (token.startsWith('eyJ')) { // JWT tokens start with eyJ (base64 encoded {"alg":...)
        try {
          // Try to decode the token manually for demo purposes
          const parts = token.split('.');
          if (parts.length === 3) {
            const decodedPayload = JSON.parse(atob(parts[1]));

            // Handle demo user
            if (decodedPayload.sub === 'demo-user-1') {
              payload = decodedPayload;
              user = {
                id: 'demo-user-1',
                username: 'demo',
                email: '<EMAIL>',
                roles: ['user'],
                permissions: ['chat:send', 'tickets:create', 'tickets:view'],
                metadata: {
                  firstName: 'Demo',
                  lastName: 'User',
                  isActive: true,
                  createdAt: new Date().toISOString(),
                  updatedAt: new Date().toISOString(),
                },
              };
            } else {
              // For non-demo tokens, verify with JWT service
              payload = this.jwtService.verify(token);
              user = await this.authService.getUserById(payload.sub);
            }
          }
        } catch (jwtError) {
          // If JWT verification fails, try manual decode for demo
          const errorMessage = jwtError instanceof Error ? jwtError.message : 'Unknown JWT error';
          console.warn('JWT verification failed, checking for demo token:', errorMessage);
          throw new WsException('Invalid token');
        }
      } else {
        throw new WsException('Invalid token format');
      }

      if (!user) {
        throw new WsException('User not found');
      }

      // Store user data in socket
      client.data.user = user;

      // Add socket to user's connections
      const userId = user.id;
      if (!this.userSockets.has(userId)) {
        this.userSockets.set(userId, new Set());
      }
      this.userSockets.get(userId).add(client.id);

      console.log(`Client connected: ${client.id}, User: ${user.username}`);
    } catch (error) {
      console.error('WebSocket connection error:', error);
      client.disconnect();
    }
  }

  handleDisconnect(client: Socket) {
    // Remove socket from user's connections
    if (client.data.user) {
      const userId = client.data.user.id;
      const userSockets = this.userSockets.get(userId);

      if (userSockets) {
        userSockets.delete(client.id);

        if (userSockets.size === 0) {
          this.userSockets.delete(userId);
        }
      }
    }

    console.log(`Client disconnected: ${client.id}`);
  }

  @SubscribeMessage(WEBSOCKET_EVENTS.MESSAGE)
  handleMessage(client: Socket, payload: any) {
    // This is handled by the HTTP endpoint, but we could implement it here as well
    return { event: WEBSOCKET_EVENTS.MESSAGE, data: 'Message received' };
  }

  @SubscribeMessage(WEBSOCKET_EVENTS.TYPING)
  handleTyping(client: Socket, payload: { sessionId: string }) {
    // Broadcast typing event to other users in the same session
    client.broadcast.emit(WEBSOCKET_EVENTS.TYPING, {
      sessionId: payload.sessionId,
      userId: client.data.user.id,
    });

    return { event: WEBSOCKET_EVENTS.TYPING, data: 'Typing event received' };
  }

  // Method to send a message to a specific user
  sendMessageToUser(userId: string, event: string, data: any) {
    const userSockets = this.userSockets.get(userId);

    if (userSockets) {
      for (const socketId of userSockets) {
        this.server.to(socketId).emit(event, data);
      }
    }
  }
}
