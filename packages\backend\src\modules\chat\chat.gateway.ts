import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  OnGatewayConnection,
  OnGatewayDisconnect,
  WsException,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { JwtService } from '@nestjs/jwt';
import { WEBSOCKET_EVENTS } from '@otrs-ai-powered/shared';
import { AuthService } from '../auth/auth.service';

@WebSocketGateway({
  cors: {
    origin: '*',
  },
})
export class ChatGateway implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  // Map to store user connections
  private userSockets = new Map<string, Set<string>>();

  constructor(
    private jwtService: JwtService,
    private authService: AuthService,
  ) {}

  async handleConnection(client: Socket) {
    try {
      // Authenticate the client
      const token = client.handshake.auth.token;
      if (!token) {
        throw new WsException('Unauthorized');
      }

      // Verify and decode the token
      const payload = this.jwtService.verify(token);
      const userId = payload.sub;

      // Get user from database
      const user = await this.authService.getUserById(userId);
      if (!user) {
        throw new WsException('User not found');
      }

      // Store user data in socket
      client.data.user = user;

      // Add socket to user's connections
      if (!this.userSockets.has(userId)) {
        this.userSockets.set(userId, new Set());
      }
      this.userSockets.get(userId).add(client.id);

      console.log(`Client connected: ${client.id}, User: ${user.username}`);
    } catch (error) {
      console.error('WebSocket connection error:', error);
      client.disconnect();
    }
  }

  handleDisconnect(client: Socket) {
    // Remove socket from user's connections
    if (client.data.user) {
      const userId = client.data.user.id;
      const userSockets = this.userSockets.get(userId);
      
      if (userSockets) {
        userSockets.delete(client.id);
        
        if (userSockets.size === 0) {
          this.userSockets.delete(userId);
        }
      }
    }

    console.log(`Client disconnected: ${client.id}`);
  }

  @SubscribeMessage(WEBSOCKET_EVENTS.MESSAGE)
  handleMessage(client: Socket, payload: any) {
    // This is handled by the HTTP endpoint, but we could implement it here as well
    return { event: WEBSOCKET_EVENTS.MESSAGE, data: 'Message received' };
  }

  @SubscribeMessage(WEBSOCKET_EVENTS.TYPING)
  handleTyping(client: Socket, payload: { sessionId: string }) {
    // Broadcast typing event to other users in the same session
    client.broadcast.emit(WEBSOCKET_EVENTS.TYPING, {
      sessionId: payload.sessionId,
      userId: client.data.user.id,
    });
    
    return { event: WEBSOCKET_EVENTS.TYPING, data: 'Typing event received' };
  }

  // Method to send a message to a specific user
  sendMessageToUser(userId: string, event: string, data: any) {
    const userSockets = this.userSockets.get(userId);
    
    if (userSockets) {
      for (const socketId of userSockets) {
        this.server.to(socketId).emit(event, data);
      }
    }
  }
}
