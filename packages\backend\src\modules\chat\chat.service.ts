import { Injectable, NotFoundException } from '@nestjs/common';
import { v4 as uuidv4 } from 'uuid';
import { ChatMessage, ChatSession, User } from '@otrs-ai-powered/shared';
import { LlmService } from '../llm/llm.service';
import { ToolCallingService } from '../tool-calling/tool-calling.service';
import { CreateSessionDto, SendMessageDto } from './dto';

// In-memory storage for chat sessions
// In a real application, this would be replaced with a database
const CHAT_SESSIONS = new Map<string, ChatSession>();

@Injectable()
export class ChatService {
  constructor(
    private llmService: LlmService,
    private toolCallingService: ToolCallingService,
  ) {}

  async createSession(user: User, createSessionDto: CreateSessionDto = {}): Promise<ChatSession> {
    const sessionId = uuidv4();
    const now = new Date().toISOString();

    // Create a system message to initialize the conversation
    const systemMessage: ChatMessage = {
      id: uuidv4(),
      content: 'I am an AI assistant for OTRS. How can I help you today?',
      role: 'system',
      timestamp: now,
    };

    const session: ChatSession = {
      id: sessionId,
      userId: user.id,
      messages: [systemMessage],
      createdAt: now,
      updatedAt: now,
      metadata: createSessionDto.metadata || {},
    };

    CHAT_SESSIONS.set(sessionId, session);

    return session;
  }

  async getSession(sessionId: string, userId: string): Promise<ChatSession> {
    const session = CHAT_SESSIONS.get(sessionId);

    if (!session || session.userId !== userId) {
      throw new NotFoundException('Chat session not found');
    }

    return session;
  }

  async getSessions(userId: string): Promise<ChatSession[]> {
    return Array.from(CHAT_SESSIONS.values())
      .filter(session => session.userId === userId)
      .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime());
  }

  async deleteSession(sessionId: string, userId: string): Promise<void> {
    const session = CHAT_SESSIONS.get(sessionId);

    if (!session || session.userId !== userId) {
      throw new NotFoundException('Chat session not found');
    }

    CHAT_SESSIONS.delete(sessionId);
  }

  async sendMessage(user: User, sendMessageDto: SendMessageDto): Promise<ChatMessage> {
    const { sessionId, content, metadata } = sendMessageDto;

    // Get the session
    const session = await this.getSession(sessionId, user.id);

    // Create user message
    const userMessageId = uuidv4();
    const now = new Date().toISOString();

    const userMessage: ChatMessage = {
      id: userMessageId,
      content,
      role: 'user',
      timestamp: now,
      metadata,
    };

    // Add user message to session
    session.messages.push(userMessage);
    session.updatedAt = now;

    // Get AI response
    const aiResponse = await this.llmService.generateResponse(session.messages);

    // Check if there are any tool calls
    if (aiResponse.toolCalls && aiResponse.toolCalls.length > 0) {
      // Execute tool calls using the provider-agnostic tool calling service
      const toolResults = await this.toolCallingService.executeToolCalls(aiResponse.toolCalls);

      // Add tool results to the context
      const toolResultMessages = toolResults.map(result => ({
        id: uuidv4(),
        content: JSON.stringify(result),
        role: 'system' as const,
        timestamp: new Date().toISOString(),
        metadata: { isToolResult: true, toolCallId: result.toolCallId },
      }));

      session.messages.push(...toolResultMessages);

      // Get final AI response with tool results
      const finalResponse = await this.llmService.generateResponse(session.messages);

      // Add AI message to session
      const aiMessage: ChatMessage = {
        id: uuidv4(),
        content: finalResponse.content,
        role: 'assistant',
        timestamp: new Date().toISOString(),
        metadata: { hasToolCalls: true },
      };

      session.messages.push(aiMessage);
      session.updatedAt = aiMessage.timestamp;

      return aiMessage;
    } else {
      // Add AI message to session
      const aiMessage: ChatMessage = {
        id: uuidv4(),
        content: aiResponse.content,
        role: 'assistant',
        timestamp: new Date().toISOString(),
      };

      session.messages.push(aiMessage);
      session.updatedAt = aiMessage.timestamp;

      return aiMessage;
    }
  }
}
