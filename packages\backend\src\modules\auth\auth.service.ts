import { Injectable, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import * as bcrypt from 'bcrypt';
import { v4 as uuidv4 } from 'uuid';
import { User, TokenResponse, JwtPayload } from '@otrs-ai-powered/shared';

// This is a mock user database for demonstration purposes
// In a real application, this would be replaced with a database service
const MOCK_USERS = [
  {
    id: '1',
    username: 'admin',
    email: '<EMAIL>',
    password: '$2b$10$EpRnTzVlqHNP0.fUbXUwSOyuiXe/QLSUG6xNekdHgTGmrpHEfIoxm', // 'password'
    roles: ['admin'],
    permissions: ['read:all', 'write:all'],
  },
  {
    id: '2',
    username: 'user',
    email: '<EMAIL>',
    password: '$2b$10$EpRnTzVlqHNP0.fUbXUwSOyuiXe/QLSUG6xNekdHgTGmrpHEfIoxm', // 'password'
    roles: ['user'],
    permissions: ['read:own', 'write:own'],
  },
];

// Store for refresh tokens
const REFRESH_TOKENS = new Map<string, { userId: string; expiresAt: Date }>();

@Injectable()
export class AuthService {
  constructor(
    private jwtService: JwtService,
    private configService: ConfigService,
  ) {}

  async validateUser(username: string, password: string): Promise<User | null> {
    const user = MOCK_USERS.find((u) => u.username === username);
    
    if (user && await bcrypt.compare(password, user.password)) {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { password, ...result } = user;
      return result as User;
    }
    
    return null;
  }

  async login(user: User): Promise<TokenResponse> {
    const payload: JwtPayload = {
      sub: user.id,
      username: user.username,
      roles: user.roles,
      permissions: user.permissions,
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + 3600, // 1 hour
    };

    const accessToken = this.jwtService.sign(payload);
    const refreshToken = uuidv4();
    
    // Store refresh token
    const refreshTokenExpiration = new Date();
    refreshTokenExpiration.setDate(refreshTokenExpiration.getDate() + 7); // 7 days
    
    REFRESH_TOKENS.set(refreshToken, {
      userId: user.id,
      expiresAt: refreshTokenExpiration,
    });

    return {
      accessToken,
      refreshToken,
      expiresIn: 3600,
      tokenType: 'Bearer',
    };
  }

  async refreshToken(refreshToken: string): Promise<TokenResponse> {
    const tokenData = REFRESH_TOKENS.get(refreshToken);
    
    if (!tokenData) {
      throw new UnauthorizedException('Invalid refresh token');
    }
    
    if (tokenData.expiresAt < new Date()) {
      REFRESH_TOKENS.delete(refreshToken);
      throw new UnauthorizedException('Refresh token expired');
    }
    
    const user = MOCK_USERS.find((u) => u.id === tokenData.userId);
    
    if (!user) {
      REFRESH_TOKENS.delete(refreshToken);
      throw new UnauthorizedException('User not found');
    }
    
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { password, ...userData } = user;
    
    // Generate new tokens
    const newTokens = await this.login(userData as User);
    
    // Remove old refresh token
    REFRESH_TOKENS.delete(refreshToken);
    
    return newTokens;
  }

  async logout(refreshToken: string): Promise<void> {
    REFRESH_TOKENS.delete(refreshToken);
  }

  async getUserById(userId: string): Promise<User | null> {
    const user = MOCK_USERS.find((u) => u.id === userId);
    
    if (!user) {
      return null;
    }
    
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { password, ...userData } = user;
    return userData as User;
  }
}
