import { ChatMessage, Tool, ToolCall } from '@otrs-ai-powered/shared';

/**
 * Configuration interface for LLM providers
 */
export interface LlmProviderConfig {
  apiKey: string;
  model: string;
  temperature?: number;
  maxTokens?: number;
  timeout?: number;
  baseUrl?: string;
}

/**
 * Response interface for LLM completions
 */
export interface LlmCompletionResponse {
  content: string;
  toolCalls?: ToolCall[];
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
}

/**
 * Abstract base interface for all LLM providers
 */
export interface ILlmProvider {
  /**
   * Generate a completion response from the LLM
   * @param messages Array of chat messages
   * @param tools Optional array of available tools
   * @returns Promise resolving to completion response
   */
  generateCompletion(
    messages: ChatMessage[],
    tools?: Tool[],
  ): Promise<LlmCompletionResponse>;

  /**
   * Validate the provider configuration
   * @returns Promise resolving to true if valid, false otherwise
   */
  validateConfig(): Promise<boolean>;

  /**
   * Get the provider name
   * @returns String identifier for the provider
   */
  getProviderName(): string;

  /**
   * Get the current model being used
   * @returns String identifier for the model
   */
  getModel(): string;
}

/**
 * Provider types supported by the system
 */
export enum LlmProviderType {
  OPENAI = 'openai',
  GOOGLE = 'google',
  ANTHROPIC = 'anthropic',
  AZURE_OPENAI = 'azure-openai',
}

/**
 * Provider factory interface for dependency injection
 */
export interface ILlmProviderFactory {
  createProvider(type: LlmProviderType, config: LlmProviderConfig): ILlmProvider;
  getSupportedProviders(): LlmProviderType[];
}
