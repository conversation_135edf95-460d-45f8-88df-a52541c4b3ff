#!/usr/bin/env node

import express from 'express';
import { randomUUID } from 'node:crypto';
import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StreamableHTTPServerTransport } from '@modelcontextprotocol/sdk/server/streamableHttp.js';
import {
  CallToolRequestSchema,
  ListToolsRequestSchema,
  Tool,
  isInitializeRequest,
} from '@modelcontextprotocol/sdk/types.js';
import { z } from 'zod';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Define tool schemas
const CreateTicketArgsSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  description: z.string().min(1, 'Description is required'),
  priority: z.enum(['low', 'medium', 'high']).default('medium'),
  queue: z.string().default('General'),
  customer: z.string().email('Valid email required'),
  tags: z.array(z.string()).optional(),
});

const SearchTicketsArgsSchema = z.object({
  query: z.string().min(1, 'Search query is required'),
  limit: z.number().min(1).max(50).default(10),
  status: z.enum(['new', 'open', 'pending', 'closed']).optional(),
});

const UpdateTicketArgsSchema = z.object({
  ticketId: z.string().min(1, 'Ticket ID is required'),
  status: z.enum(['new', 'open', 'pending', 'closed']).optional(),
  priority: z.enum(['low', 'medium', 'high']).optional(),
  assignee: z.string().optional(),
  comment: z.string().optional(),
});

// Mock OTRS functions
function createOtrsTicket(args: z.infer<typeof CreateTicketArgsSchema>) {
  const ticketId = `TICKET-${Math.floor(Math.random() * 10000)}`;

  return {
    success: true,
    ticketId,
    message: `Ticket ${ticketId} created successfully`,
    ticket: {
      id: ticketId,
      title: args.title,
      description: args.description,
      priority: args.priority,
      queue: args.queue,
      customer: args.customer,
      tags: args.tags || [],
      status: 'new',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
  };
}

function searchOtrsTickets(args: z.infer<typeof SearchTicketsArgsSchema>) {
  const tickets = [];
  const numResults = Math.min(args.limit, Math.floor(Math.random() * 5) + 1);

  for (let i = 0; i < numResults; i++) {
    tickets.push({
      id: `TICKET-${Math.floor(Math.random() * 10000)}`,
      title: `Sample ticket ${i + 1} matching "${args.query}"`,
      description: `This is a sample ticket description for query: ${args.query}`,
      priority: ['low', 'medium', 'high'][Math.floor(Math.random() * 3)],
      status: args.status || ['new', 'open', 'pending', 'closed'][Math.floor(Math.random() * 4)],
      queue: 'General',
      customer: '<EMAIL>',
      createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
    });
  }

  return {
    success: true,
    message: `Found ${tickets.length} tickets matching "${args.query}"`,
    tickets,
    total: tickets.length,
  };
}

function updateOtrsTicket(args: z.infer<typeof UpdateTicketArgsSchema>) {
  return {
    success: true,
    ticketId: args.ticketId,
    message: `Ticket ${args.ticketId} updated successfully`,
    ticket: {
      id: args.ticketId,
      status: args.status || 'open',
      priority: args.priority || 'medium',
      assignee: args.assignee || 'unassigned',
      updatedAt: new Date().toISOString(),
    },
    comment: args.comment ? {
      id: `COMMENT-${Math.floor(Math.random() * 1000)}`,
      content: args.comment,
      author: 'system',
      createdAt: new Date().toISOString(),
    } : null,
  };
}

// Create MCP server
const server = new Server(
  {
    name: 'otrs-mcp-server',
    version: '1.0.0',
  },
  {
    capabilities: {
      tools: {},
    },
  }
);

// Define available tools
const tools: Tool[] = [
  {
    name: 'create_ticket',
    description: 'Create a new OTRS ticket with the specified details',
    inputSchema: {
      type: 'object',
      properties: {
        title: {
          type: 'string',
          description: 'The title/subject of the ticket',
        },
        description: {
          type: 'string',
          description: 'Detailed description of the issue or request',
        },
        priority: {
          type: 'string',
          enum: ['low', 'medium', 'high'],
          description: 'Priority level of the ticket',
          default: 'medium',
        },
        queue: {
          type: 'string',
          description: 'The queue/department to assign the ticket to',
          default: 'General',
        },
        customer: {
          type: 'string',
          description: 'Email address of the customer/requester',
        },
        tags: {
          type: 'array',
          items: { type: 'string' },
          description: 'Optional tags to categorize the ticket',
        },
      },
      required: ['title', 'description', 'customer'],
    },
  },
  {
    name: 'search_tickets',
    description: 'Search for existing OTRS tickets based on query criteria',
    inputSchema: {
      type: 'object',
      properties: {
        query: {
          type: 'string',
          description: 'Search query to find tickets',
        },
        limit: {
          type: 'number',
          description: 'Maximum number of tickets to return',
          minimum: 1,
          maximum: 50,
          default: 10,
        },
        status: {
          type: 'string',
          enum: ['new', 'open', 'pending', 'closed'],
          description: 'Filter tickets by status',
        },
      },
      required: ['query'],
    },
  },
  {
    name: 'update_ticket',
    description: 'Update an existing OTRS ticket',
    inputSchema: {
      type: 'object',
      properties: {
        ticketId: {
          type: 'string',
          description: 'The ID of the ticket to update',
        },
        status: {
          type: 'string',
          enum: ['new', 'open', 'pending', 'closed'],
          description: 'New status for the ticket',
        },
        priority: {
          type: 'string',
          enum: ['low', 'medium', 'high'],
          description: 'New priority for the ticket',
        },
        assignee: {
          type: 'string',
          description: 'Assign the ticket to a specific user',
        },
        comment: {
          type: 'string',
          description: 'Add a comment to the ticket',
        },
      },
      required: ['ticketId'],
    },
  },
];

// Handle tool listing
server.setRequestHandler(ListToolsRequestSchema, async () => {
  return { tools };
});

// Handle tool calls
server.setRequestHandler(CallToolRequestSchema, async (request) => {
  const { name, arguments: args } = request.params;

  try {
    switch (name) {
      case 'create_ticket': {
        const validatedArgs = CreateTicketArgsSchema.parse(args);
        const result = createOtrsTicket(validatedArgs);
        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify(result, null, 2),
            },
          ],
        };
      }

      case 'search_tickets': {
        const validatedArgs = SearchTicketsArgsSchema.parse(args);
        const result = searchOtrsTickets(validatedArgs);
        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify(result, null, 2),
            },
          ],
        };
      }

      case 'update_ticket': {
        const validatedArgs = UpdateTicketArgsSchema.parse(args);
        const result = updateOtrsTicket(validatedArgs);
        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify(result, null, 2),
            },
          ],
        };
      }

      default:
        throw new Error(`Unknown tool: ${name}`);
    }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify({ error: errorMessage }, null, 2),
        },
      ],
      isError: true,
    };
  }
});

// Start the HTTP server
async function main() {
  const app = express();
  app.use(express.json());

  // Map to store transports by session ID
  const transports: { [sessionId: string]: StreamableHTTPServerTransport } = {};

  // Handle POST requests for client-to-server communication
  app.post('/mcp', async (req, res) => {
    // Check for existing session ID
    const sessionId = req.headers['mcp-session-id'] as string | undefined;
    let transport: StreamableHTTPServerTransport;

    if (sessionId && transports[sessionId]) {
      // Reuse existing transport
      transport = transports[sessionId];
    } else if (!sessionId && isInitializeRequest(req.body)) {
      // New initialization request
      transport = new StreamableHTTPServerTransport({
        sessionIdGenerator: () => randomUUID(),
        onsessioninitialized: (sessionId) => {
          // Store the transport by session ID
          transports[sessionId] = transport;
        }
      });

      // Clean up transport when closed
      transport.onclose = () => {
        if (transport.sessionId) {
          delete transports[transport.sessionId];
        }
      };

      // Connect to the MCP server
      await server.connect(transport);
    } else {
      // Invalid request
      res.status(400).json({
        jsonrpc: '2.0',
        error: {
          code: -32000,
          message: 'Bad Request: No valid session ID provided',
        },
        id: null,
      });
      return;
    }

    // Handle the request
    await transport.handleRequest(req, res, req.body);
  });

  // Reusable handler for GET and DELETE requests
  const handleSessionRequest = async (req: express.Request, res: express.Response) => {
    const sessionId = req.headers['mcp-session-id'] as string | undefined;
    if (!sessionId || !transports[sessionId]) {
      res.status(400).send('Invalid or missing session ID');
      return;
    }

    const transport = transports[sessionId];
    await transport.handleRequest(req, res);
  };

  // Handle GET requests for server-to-client notifications via SSE
  app.get('/mcp', handleSessionRequest);

  // Handle DELETE requests for session termination
  app.delete('/mcp', handleSessionRequest);

  const port = process.env.MCP_PORT ?? 5001;
  app.listen(port, () => {
    console.log(`OTRS MCP Server running on HTTP port ${port}`);
  });
}

main().catch((error) => {
  console.error('Server error:', error);
  process.exit(1);
});
