/**
 * Represents a document in the RAG system
 */
export interface Document {
  id: string;
  content: string;
  metadata: DocumentMetadata;
}

/**
 * Metadata for a document
 */
export interface DocumentMetadata {
  source: string;
  title?: string;
  author?: string;
  createdAt?: string;
  updatedAt?: string;
  url?: string;
  tags?: string[];
  category?: string;
  [key: string]: any;
}

/**
 * Represents a document with its embedding
 */
export interface DocumentWithEmbedding extends Document {
  embedding: number[];
}
