import { registerAs } from '@nestjs/config';

export default registerAs('llm', () => ({
  provider: process.env.LLM_PROVIDER || 'google',
  apiKey: process.env.LLM_API_KEY,
  model: process.env.LLM_MODEL || 'gemini-2.0-flash',
  temperature: parseFloat(process.env.LLM_TEMPERATURE || '0.7'),
  maxTokens: parseInt(process.env.LLM_MAX_TOKENS || '2000', 10),
  timeout: parseInt(process.env.LLM_TIMEOUT || '30000', 10),
  baseUrl: process.env.LLM_BASE_URL,
}));
