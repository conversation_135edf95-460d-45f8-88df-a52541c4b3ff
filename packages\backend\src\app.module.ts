import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { AuthModule } from './modules/auth/auth.module';
import { ChatModule } from './modules/chat/chat.module';
import { LlmModule } from './modules/llm/llm.module';
import { McpClientModule } from './modules/mcp-client/mcp-client.module';

@Module({
  imports: [
    // Configuration
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
    }),
    
    // Feature modules
    AuthModule,
    ChatModule,
    LlmModule,
    McpClientModule,
  ],
  controllers: [],
  providers: [],
})
export class AppModule {}
