import { Injectable, Logger } from '@nestjs/common';
import { GoogleGenerativeAI, GenerativeModel, Part } from '@google/generative-ai';
import { ChatMessage, Tool, ToolCall } from '@otrs-ai-powered/shared';
import { ILlmProvider, LlmProviderConfig, LlmCompletionResponse } from '../interfaces/llm-provider.interface';

@Injectable()
export class GoogleProvider implements ILlmProvider {
  private readonly logger = new Logger(GoogleProvider.name);
  private readonly genAI: GoogleGenerativeAI;
  private readonly model: GenerativeModel;
  private readonly config: LlmProviderConfig;

  constructor(config: LlmProviderConfig) {
    this.config = config;
    this.genAI = new GoogleGenerativeAI(config.apiKey);
    this.model = this.genAI.getGenerativeModel({
      model: config.model,
      generationConfig: {
        temperature: config.temperature ?? 0.7,
        maxOutputTokens: config.maxTokens ?? 2000,
      },
    });
  }

  async generateCompletion(
    messages: ChatMessage[],
    tools?: Tool[],
  ): Promise<LlmCompletionResponse> {
    try {
      this.logger.debug(`Generating completion with ${messages.length} messages`);

      // Convert messages to Gemini format
      const geminiMessages = this.convertMessagesToGeminiFormat(messages);

      // Create the chat session with tools if provided
      const chatConfig: any = {
        history: geminiMessages.slice(0, -1), // All but the last message
      };

      // Add tools configuration if tools are provided
      if (tools && tools.length > 0) {
        chatConfig.tools = this.convertToolsToGeminiFormat(tools);
      }

      const chat = this.model.startChat(chatConfig);

      // Get the last message content
      const lastMessage = geminiMessages[geminiMessages.length - 1];
      const prompt = this.extractTextFromParts(lastMessage.parts);

      // Generate response
      const result = await chat.sendMessage(prompt);
      const response = result.response;

      // Check if the response contains function calls
      const functionCalls = response.functionCalls();
      if (functionCalls && functionCalls.length > 0) {
        // Convert function calls to tool calls
        const toolCalls: ToolCall[] = functionCalls.map((call, index) => ({
          toolId: `tool_${Date.now()}_${index}`,
          toolName: call.name,
          arguments: call.args,
        }));

        return {
          content: 'I need to execute some tools to help you with that.',
          toolCalls,
          usage: {
            promptTokens: 0,
            completionTokens: 0,
            totalTokens: 0,
          },
        };
      }

      // Regular text response
      const text = response.text();
      this.logger.debug(`Generated response: ${text.substring(0, 100)}...`);

      return {
        content: text,
        usage: {
          promptTokens: 0, // Gemini doesn't provide detailed token usage in free tier
          completionTokens: 0,
          totalTokens: 0,
        },
      };
    } catch (error) {
      this.logger.error('Error generating completion:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new Error(`Google Gemini API error: ${errorMessage}`);
    }
  }

  private convertMessagesToGeminiFormat(messages: ChatMessage[]): any[] {
    return messages
      .filter(msg => msg.role !== 'system') // Gemini doesn't have system messages
      .map(msg => ({
        role: msg.role === 'assistant' ? 'model' : 'user',
        parts: [{ text: msg.content }],
      }));
  }

  private extractTextFromParts(parts: Part[]): string {
    return parts
      .filter(part => 'text' in part)
      .map(part => part.text)
      .join(' ');
  }

  private convertToolsToGeminiFormat(tools: Tool[]): any[] {
    return tools.map(tool => ({
      functionDeclarations: [{
        name: tool.name,
        description: tool.description,
        parameters: tool.parameters,
      }],
    }));
  }

  async validateConfig(): Promise<boolean> {
    try {
      if (!this.config.apiKey) {
        this.logger.error('Google API key is missing');
        return false;
      }

      // Test the API with a simple request
      const testResult = await this.model.generateContent('Hello');
      const response = testResult.response;
      const text = response.text();

      this.logger.debug('Google Gemini API validation successful');
      return !!text;
    } catch (error) {
      this.logger.error('Google Gemini API validation failed:', error);
      return false;
    }
  }

  getProviderName(): string {
    return 'google';
  }

  getModel(): string {
    return this.config.model;
  }
}
