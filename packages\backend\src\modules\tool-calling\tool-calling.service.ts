import { Injectable, Logger, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StreamableHTTPClientTransport } from '@modelcontextprotocol/sdk/client/streamableHttp.js';
import { ToolCall, ToolCallResult } from '@otrs-ai-powered/shared';
import { IToolCallingService, ToolCallingConfig } from './interfaces/tool-calling.interface';

@Injectable()
export class ToolCallingService implements IToolCallingService, OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(ToolCallingService.name);
  private client: Client | null = null;
  private transport: StreamableHTTPClientTransport | null = null;
  private config: ToolCallingConfig;
  private isConnected = false;

  constructor(private configService: ConfigService) {
    this.config = {
      mcpServerUrl: this.configService.get<string>('MCP_SERVER_URL', 'http://localhost:5001'),
      timeout: this.configService.get<number>('MCP_TIMEOUT', 30000),
      retryAttempts: this.configService.get<number>('MCP_RETRY_ATTEMPTS', 3),
    };
  }

  async onModuleInit() {
    await this.initializeConnection();
  }

  async onModuleDestroy() {
    await this.disconnect();
  }

  private async initializeConnection(): Promise<void> {
    try {
      this.logger.log(`Connecting to MCP server at ${this.config.mcpServerUrl}`);
      this.logger.debug(`MCP_SERVER_URL env var: ${process.env.MCP_SERVER_URL}`);

      // Create client
      this.client = new Client({
        name: 'otrs-backend-client',
        version: '1.0.0',
      });

      // Create transport
      this.transport = new StreamableHTTPClientTransport(
        new URL(`${this.config.mcpServerUrl}/mcp`)
      );

      // Connect
      await this.client.connect(this.transport);
      this.isConnected = true;

      this.logger.log('Successfully connected to MCP server');
    } catch (error) {
      this.logger.error('Failed to connect to MCP server:', error);
      this.isConnected = false;
      throw error;
    }
  }

  private async disconnect(): Promise<void> {
    if (this.transport) {
      try {
        await this.transport.close();
        this.logger.log('Disconnected from MCP server');
      } catch (error) {
        this.logger.error('Error disconnecting from MCP server:', error);
      }
    }
    this.isConnected = false;
    this.client = null;
    this.transport = null;
  }

  async executeToolCall(toolCall: ToolCall): Promise<ToolCallResult> {
    if (!this.isConnected || !this.client) {
      throw new Error('Tool calling service is not connected to MCP server');
    }

    try {
      this.logger.debug(`Executing tool call: ${toolCall.toolName}`);

      const result = await this.client.callTool({
        name: toolCall.toolName,
        arguments: toolCall.arguments,
      });

      return {
        toolCallId: toolCall.toolId,
        result: result.content,
        error: result.isError ? 'Tool execution failed' : undefined,
      };
    } catch (error) {
      this.logger.error(`Error executing tool call ${toolCall.toolName}:`, error);

      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      return {
        toolCallId: toolCall.toolId,
        error: errorMessage,
        result: null,
      };
    }
  }

  async executeToolCalls(toolCalls: ToolCall[]): Promise<ToolCallResult[]> {
    if (!this.isConnected || !this.client) {
      throw new Error('Tool calling service is not connected to MCP server');
    }

    this.logger.debug(`Executing ${toolCalls.length} tool calls`);

    // Execute all tool calls in parallel
    const results = await Promise.all(
      toolCalls.map(toolCall => this.executeToolCall(toolCall))
    );

    return results;
  }

  async isAvailable(): Promise<boolean> {
    if (!this.isConnected || !this.client) {
      return false;
    }

    try {
      // Test connection by listing available tools
      await this.client.listTools();
      return true;
    } catch (error) {
      this.logger.warn('MCP server availability check failed:', error);
      return false;
    }
  }

  /**
   * Reconnect to the MCP server if connection is lost
   */
  async reconnect(): Promise<void> {
    this.logger.log('Attempting to reconnect to MCP server');
    await this.disconnect();
    await this.initializeConnection();
  }

  /**
   * Get available tools from the MCP server
   */
  async getAvailableTools(): Promise<any[]> {
    if (!this.isConnected || !this.client) {
      throw new Error('Tool calling service is not connected to MCP server');
    }

    try {
      const response = await this.client.listTools();
      return response.tools;
    } catch (error) {
      this.logger.error('Error getting available tools:', error);
      throw error;
    }
  }
}
