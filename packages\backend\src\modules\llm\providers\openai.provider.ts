import { Injectable, Logger } from '@nestjs/common';
import OpenAI from 'openai';
import { ChatMessage, Tool, ToolCall } from '@otrs-ai-powered/shared';
import { ILlmProvider, LlmProviderConfig, LlmCompletionResponse } from '../interfaces/llm-provider.interface';

@Injectable()
export class OpenAIProvider implements ILlmProvider {
  private readonly logger = new Logger(OpenAIProvider.name);
  private openai: OpenAI;
  private config: LlmProviderConfig;

  constructor(config: LlmProviderConfig) {
    this.config = config;
    this.openai = new OpenAI({
      apiKey: config.apiKey,
      baseURL: config.baseUrl,
      timeout: config.timeout || 30000,
    });
  }

  async generateCompletion(
    messages: ChatMessage[],
    tools?: Tool[],
  ): Promise<LlmCompletionResponse> {
    try {
      this.logger.debug(`Generating completion with ${messages.length} messages`);

      // Convert messages to OpenAI format
      const openaiMessages = messages.map(msg => ({
        role: msg.role as 'system' | 'user' | 'assistant',
        content: msg.content,
      }));

      // Check if this looks like a ticket creation request
      const lastMessage = messages[messages.length - 1];
      if (this.shouldCreateTicket(lastMessage.content)) {
        return this.generateTicketCreationResponse(lastMessage.content);
      }

      // For demo purposes, use a simple response
      // In production, this would make actual OpenAI API calls
      const completion = await this.generateMockCompletion(openaiMessages);

      return {
        content: completion.content,
        usage: {
          promptTokens: 0,
          completionTokens: 0,
          totalTokens: 0,
        },
      };
    } catch (error) {
      this.logger.error('Error generating completion:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new Error(`OpenAI API error: ${errorMessage}`);
    }
  }

  private async generateMockCompletion(messages: any[]): Promise<{ content: string }> {
    // Mock implementation for demo purposes
    // In production, replace with actual OpenAI API call:
    // const completion = await this.openai.chat.completions.create({
    //   model: this.config.model,
    //   messages,
    //   temperature: this.config.temperature,
    //   max_tokens: this.config.maxTokens,
    // });
    // return { content: completion.choices[0].message.content };

    return {
      content: "I'm here to help with your OTRS-related questions. How can I assist you today?",
    };
  }

  private shouldCreateTicket(content: string): boolean {
    const lowerContent = content.toLowerCase();
    return (
      lowerContent.includes('create ticket') ||
      lowerContent.includes('new ticket') ||
      lowerContent.includes('open ticket') ||
      lowerContent.includes('submit ticket') ||
      lowerContent.includes('ticket for') ||
      lowerContent.includes('need help with') ||
      lowerContent.includes('having issues') ||
      lowerContent.includes('problem with')
    );
  }

  private generateTicketCreationResponse(content: string): LlmCompletionResponse {
    return {
      content: 'I can help you create a ticket. Let me process that for you.',
      toolCalls: [
        {
          toolId: `ticket_${Date.now()}`,
          toolName: 'create_ticket',
          arguments: {
            title: 'Sample ticket from OpenAI',
            description: `User request: ${content}`,
            priority: 'medium',
            queue: 'Support',
            customer: '<EMAIL>',
            tags: ['ai-generated'],
          },
        },
      ],
    };
  }

  async validateConfig(): Promise<boolean> {
    try {
      if (!this.config.apiKey) {
        this.logger.error('OpenAI API key is missing');
        return false;
      }

      // For demo purposes, always return true
      // In production, test with a simple API call
      this.logger.debug('OpenAI API validation successful (mock)');
      return true;
    } catch (error) {
      this.logger.error('OpenAI API validation failed:', error);
      return false;
    }
  }

  getProviderName(): string {
    return 'openai';
  }

  getModel(): string {
    return this.config.model;
  }
}
