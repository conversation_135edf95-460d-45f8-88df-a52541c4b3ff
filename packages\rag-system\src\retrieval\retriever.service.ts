import { Document } from '../types';
import { VectorDbService } from '../storage/vector-db.service';
import { EmbeddingService } from '../embeddings/embedding.service';
import { logger } from '../utils/logger';

/**
 * Service for retrieving relevant documents
 */
export class RetrieverService {
  constructor(
    private vectorDbService: VectorDbService,
    private embeddingService: EmbeddingService
  ) {
    logger.info('Initializing retriever service');
  }

  /**
   * Retrieve documents relevant to a query
   * @param query Query text
   * @param topK Number of results to return
   * @returns Array of relevant documents with scores
   */
  async retrieveRelevantDocuments(
    query: string,
    topK: number = 5
  ): Promise<Array<{ document: Document; score: number }>> {
    try {
      logger.info(`Retrieving documents for query: ${query}`);
      
      // Generate embedding for the query
      const queryEmbedding = await this.embeddingService.generateEmbedding(query);
      
      // Search for similar documents
      const results = await this.vectorDbService.similaritySearch(queryEmbedding, topK);
      
      logger.info(`Retrieved ${results.length} documents`);
      
      return results;
    } catch (error) {
      logger.error('Error retrieving documents:', error);
      throw new Error(`Failed to retrieve documents: ${error.message}`);
    }
  }

  /**
   * Retrieve documents using hybrid search (vector + keyword)
   * @param query Query text
   * @param filters Optional metadata filters
   * @param topK Number of results to return
   * @returns Array of relevant documents with scores
   */
  async hybridSearch(
    query: string,
    filters?: Record<string, any>,
    topK: number = 5
  ): Promise<Array<{ document: Document; score: number }>> {
    try {
      logger.info(`Performing hybrid search for query: ${query}`);
      
      // In a real implementation, this would combine vector search with keyword search
      // For now, we'll just use the vector search
      
      const results = await this.retrieveRelevantDocuments(query, topK);
      
      // Apply filters if provided
      if (filters) {
        return results.filter(result => {
          // Check if document metadata matches all filters
          return Object.entries(filters).every(([key, value]) => {
            return result.document.metadata[key] === value;
          });
        });
      }
      
      return results;
    } catch (error) {
      logger.error('Error performing hybrid search:', error);
      throw new Error(`Failed to perform hybrid search: ${error.message}`);
    }
  }
}
